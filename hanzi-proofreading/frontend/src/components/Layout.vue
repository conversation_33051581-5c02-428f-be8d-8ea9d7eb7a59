<template>
  <div class="layout-container">
    <!-- 侧边栏 -->
    <el-aside width="250px" class="sidebar">
      <div class="logo">
        <div class="logo-content">
          <div class="logo-icon">
            <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
              <!-- 外框 -->
              <rect x="2" y="2" width="24" height="24" rx="3" stroke="currentColor" stroke-width="1.5" fill="none"/>
              <!-- 汉字结构线条 -->
              <path d="M7 9 L21 9" stroke="currentColor" stroke-width="1.2"/>
              <path d="M7 14 L21 14" stroke="currentColor" stroke-width="1.2"/>
              <path d="M7 19 L21 19" stroke="currentColor" stroke-width="1.2"/>
              <path d="M10 6 L10 22" stroke="currentColor" stroke-width="1.2"/>
              <path d="M18 6 L18 22" stroke="currentColor" stroke-width="1.2"/>
              <!-- 纹路装饰 -->
              <path d="M14 6 L14 11" stroke="currentColor" stroke-width="0.8" opacity="0.6"/>
              <path d="M14 17 L14 22" stroke="currentColor" stroke-width="0.8" opacity="0.6"/>
              <!-- 中心强调点 -->
              <circle cx="14" cy="14" r="2" fill="currentColor" opacity="0.8"/>
              <circle cx="14" cy="14" r="1" fill="white"/>
            </svg>
          </div>
          <h2>汉字纹路系统</h2>
        </div>
      </div>
      
      <el-menu
        :default-active="activeMenu"
        class="sidebar-menu"
        @select="handleMenuSelect"
        background-color="#f8fafc"
        text-color="#475569"
        active-text-color="#1890ff"
      >
        <!-- 仪表板 -->
        <el-sub-menu index="dashboard">
          <template #title>
            <el-icon class="menu-icon"><DataBoard /></el-icon>
            <span>仪表板</span>
          </template>
          <el-menu-item index="dashboard-guangyun">广韵概览</el-menu-item>
          <el-menu-item index="dashboard-hanzi">字形概览</el-menu-item>
        </el-sub-menu>

        <!-- 字形 -->
        <el-sub-menu index="zixing">
          <template #title>
            <el-icon class="menu-icon"><Document /></el-icon>
            <span>字形</span>
          </template>
          <el-menu-item index="zixing-hanzi-detail">汉字详情</el-menu-item>
          <el-menu-item index="zixing-relation-proofread">关系校对</el-menu-item>
        </el-sub-menu>

        <!-- 音韵 -->
        <el-sub-menu index="yinyun">
          <template #title>
            <el-icon class="menu-icon"><ChatLineRound /></el-icon>
            <span>音韵</span>
          </template>
          <el-menu-item index="yinyun-guangyun">广韵校对</el-menu-item>
        </el-sub-menu>

        <!-- 其他菜单项可以在这里添加 -->
      </el-menu>
    </el-aside>

    <!-- 主内容区域 -->
    <el-container class="main-container">
      <!-- 顶部导航栏 -->
      <el-header class="header">
        <div class="header-content">
          <div class="breadcrumb">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item>{{ currentSection }}</el-breadcrumb-item>
              <el-breadcrumb-item>{{ currentPage }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>

          <!-- 统一搜索组件 -->
          <div class="header-search" v-if="showSearch">
            <UnifiedSearch
              v-model="searchQuery"
              :loading="searchLoading"
              :placeholder="searchPlaceholder"
              :show-search-button="searchConfig.showButton"
              :search-mode="searchConfig.mode"
              @search="handleSearch"
            />
          </div>

          <div class="header-actions">

          </div>
        </div>
      </el-header>

      <!-- 主内容 -->
      <el-main class="content">
        <slot></slot>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import { DataBoard, Document, ChatLineRound } from '@element-plus/icons-vue'
import UnifiedSearch from './UnifiedSearch.vue'

export default {
  name: 'Layout',
  components: {
    DataBoard,
    Document,
    ChatLineRound,
    UnifiedSearch
  },
  data() {
    return {
      activeMenu: 'dashboard-overview',
      searchQuery: '',
      searchLoading: false
    }
  },
  computed: {
    currentSection() {
      if (this.activeMenu.startsWith('dashboard')) {
        return '仪表板'
      } else if (this.activeMenu.startsWith('zixing')) {
        return '字形'
      } else if (this.activeMenu.startsWith('yinyun')) {
        return '音韵'
      }
      return ''
    },
    currentPage() {
      const menuMap = {
        'dashboard-guangyun': '广韵概览',
        'dashboard-hanzi': '字形概览',
        'zixing-hanzi-detail': '汉字详情',
        'zixing-relation-proofread': '关系校对',
        'yinyun-guangyun': '广韵校对'
      }
      return menuMap[this.activeMenu] || ''
    },

    // 是否显示搜索框
    showSearch() {
      return ['zixing-hanzi-detail', 'zixing-relation-proofread', 'yinyun-guangyun'].includes(this.activeMenu)
    },

    // 搜索框占位符
    searchPlaceholder() {
      const placeholderMap = {
        'zixing-hanzi-detail': '输入汉字或Unicode编码搜索（如：汉、6C49、U+6C49）...',
        'zixing-relation-proofread': '输入汉字或Unicode编码搜索（如：汉、6C49、U+6C49）...',
        'yinyun-guangyun': '输入汉字或Unicode编码搜索（如：汉、6C49、U+6C49）...'
      }
      return placeholderMap[this.activeMenu] || '输入汉字或Unicode编码搜索...'
    },

    // 搜索配置
    searchConfig() {
      const configMap = {
        'zixing-hanzi-detail': { mode: 'enter', showButton: false },
        'zixing-relation-proofread': { mode: 'enter', showButton: false },
        'yinyun-guangyun': { mode: 'enter', showButton: false }
      }
      return configMap[this.activeMenu] || { mode: 'enter', showButton: false }
    }
  },
  methods: {
    handleMenuSelect(index) {
      this.activeMenu = index
      // 切换页面时清空搜索内容
      this.searchQuery = ''
      this.$emit('menu-select', index)
    },

    handleSearch(query) {
      this.searchLoading = true
      // 向当前活动页面发送搜索事件
      this.$emit('search', {
        query,
        page: this.activeMenu
      })
    },

    // 供外部调用，用于控制搜索状态
    setSearchLoading(loading) {
      this.searchLoading = loading
    },

    // 供外部调用，用于设置搜索内容
    setSearchQuery(query) {
      this.searchQuery = query
    },

    // 供外部调用，用于设置活动菜单
    setActiveMenu(menuIndex) {
      this.activeMenu = menuIndex
    }
  },
  mounted() {
    // 默认设置为广韵概览菜单
    this.activeMenu = 'dashboard-guangyun'
  }
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
  display: flex;
}

.sidebar {
  background-color: #f8fafc;
  border-right: 1px solid #e2e8f0;
  overflow: hidden;
}

.logo {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #e2e8f0;
}

.logo-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.logo-icon {
  color: #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo h2 {
  color: #1e293b;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.sidebar-menu {
  border: none;
  height: calc(100vh - 80px);
}

.main-container {
  flex: 1;
  background-color: #ffffff;
}

.header {
  background-color: #ffffff;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 24px;
  display: flex;
  align-items: center;
  height: 64px;
}

.header-content {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
}

.breadcrumb {
  font-size: 14px;
  flex-shrink: 0;
}

.header-search {
  flex: 1;
  display: flex;
  align-items: center;
  max-width: 600px;
  margin: 0 2rem;
}

.header-actions {
  flex-shrink: 0;
}

.content {
  padding: 24px;
  background-color: #f8fafc;
  min-height: calc(100vh - 64px);
}

/* Element Plus 菜单样式覆盖 */
:deep(.el-sub-menu__title) {
  color: #475569 !important;
  font-weight: 500;
}

:deep(.el-sub-menu__title:hover) {
  background-color: #e2e8f0 !important;
  color: #1e293b !important;
}

:deep(.el-menu-item) {
  color: #64748b !important;
  font-weight: 400;
}

:deep(.el-menu-item:hover) {
  background-color: #e2e8f0 !important;
  color: #1890ff !important;
}

:deep(.el-menu-item.is-active) {
  background-color: #e0f2ff !important;
  color: #1890ff !important;
  font-weight: 500;
}

:deep(.el-sub-menu.is-active > .el-sub-menu__title) {
  color: #1890ff !important;
}

.menu-icon {
  color: #64748b;
  margin-right: 8px;
}

:deep(.el-sub-menu__title:hover .menu-icon) {
  color: #1890ff;
}

:deep(.el-sub-menu.is-active .menu-icon) {
  color: #1890ff;
}
</style>
