<template>
  <div id="app">
    <Layout
      ref="layoutRef"
      @menu-select="handleMenuSelect"
      @search="handleSearch"
    >
      <HanziDetail
        v-if="currentView === 'hanzi-detail'"
        ref="hanziDetailRef"
        @navigate-to-relation="handleNavigateToRelation"
        @search-loading="handleSearchLoading"
      />
      <RelationProofread
        v-else-if="currentView === 'relation-proofread'"
        ref="relationProofreadRef"
        :hanzi="selectedHanzi"
        @back-to-home="handleBackToHome"
        @search-loading="handleSearchLoading"
      />
      <Guangyun
        v-else-if="currentView === 'guangyun'"
        ref="guangyunRef"
        @search-loading="handleSearchLoading"
      />
      <GuangyunDashboard
        v-else-if="currentView === 'guangyun-dashboard'"
      />
      <HanziDashboard
        v-else-if="currentView === 'hanzi-dashboard'"
      />
      <div v-else class="placeholder-content">
        <el-empty description="请从左侧菜单选择功能"></el-empty>
      </div>
    </Layout>
  </div>
</template>

<script>
import Layout from './components/Layout.vue'
import HanziDetail from './views/HanziDetail.vue'
import RelationProofread from './views/RelationProofread.vue'
import Guangyun from './views/Guangyun.vue'
import GuangyunDashboard from './views/GuangyunDashboard.vue'
import HanziDashboard from './views/HanziDashboard.vue'

export default {
  name: 'App',
  components: {
    Layout,
    HanziDetail,
    RelationProofread,
    Guangyun,
    GuangyunDashboard,
    HanziDashboard
  },
  data() {
    return {
      currentView: 'guangyun-dashboard', // 默认显示广韵概览
      selectedHanzi: ''
    }
  },
  methods: {
    handleMenuSelect(menuIndex) {
      // 根据菜单选择切换视图
      if (menuIndex === 'zixing-hanzi-detail') {
        this.currentView = 'hanzi-detail'
      } else if (menuIndex === 'zixing-relation-proofread') {
        this.currentView = 'relation-proofread'
      } else if (menuIndex === 'yinyun-guangyun') {
        this.currentView = 'guangyun'
      } else if (menuIndex === 'dashboard-guangyun') {
        this.currentView = 'guangyun-dashboard'
      } else if (menuIndex === 'dashboard-hanzi') {
        this.currentView = 'hanzi-dashboard'
      }
    },

    handleSearch(searchData) {
      const { query, page } = searchData

      // 根据当前页面调用对应的搜索方法
      try {
        if (page === 'zixing-hanzi-detail' && this.$refs.hanziDetailRef) {
          this.$refs.hanziDetailRef.performSearch(query)
        } else if (page === 'zixing-relation-proofread' && this.$refs.relationProofreadRef) {
          this.$refs.relationProofreadRef.performSearch(query)
        } else if (page === 'yinyun-guangyun' && this.$refs.guangyunRef) {
          this.$refs.guangyunRef.performSearch(query)
        }
      } catch (error) {
        console.error('搜索调用失败:', error)
        // 重置搜索状态
        if (this.$refs.layoutRef) {
          this.$refs.layoutRef.setSearchLoading(false)
        }
      }
    },

    handleSearchLoading(loading) {
      // 控制Layout中的搜索loading状态
      if (this.$refs.layoutRef) {
        this.$refs.layoutRef.setSearchLoading(loading)
      }
    },

    handleNavigateToRelation(hanzi) {
      // 如果传入的是对象，提取字符；如果是字符串，直接使用
      if (typeof hanzi === 'object' && hanzi.character) {
        this.selectedHanzi = hanzi.character
      } else if (typeof hanzi === 'string') {
        this.selectedHanzi = hanzi
      } else {
        this.selectedHanzi = hanzi
      }
      this.currentView = 'relation-proofread'
    },

    handleBackToHome() {
      this.currentView = 'hanzi-detail'
      this.selectedHanzi = ''
    },

    handleNavigateToGuangyun(event) {
      // 处理从Dashboard跳转到广韵校对页面的事件
      const { hanzi, unicode, fanQie } = event.detail

      // 切换到广韵校对页面
      this.currentView = 'guangyun'

      // 通知Layout组件更新菜单选择
      if (this.$refs.layoutRef) {
        this.$refs.layoutRef.setActiveMenu('yinyun-guangyun')
      }

      // 等待页面切换完成后，触发搜索
      this.$nextTick(() => {
        if (this.$refs.guangyunRef) {
          this.$refs.guangyunRef.performSearch(hanzi)
        }
      })
    }
  },

  mounted() {
    // 监听从Dashboard跳转到广韵校对页面的事件
    window.addEventListener('navigate-to-guangyun', this.handleNavigateToGuangyun)
  },

  beforeUnmount() {
    // 清理事件监听器
    window.removeEventListener('navigate-to-guangyun', this.handleNavigateToGuangyun)
  }
}
</script>

<style>
@import url('https://fonts.googleapis.com/css2?family=Public+Sans:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');

:root {
  --primary-color: #1990e5;
  --background-color: #f8fafc;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --input-bg: #ffffff;
  --card-bg: #ffffff;
  --chinese-font-family: 'HanaMinA', 'HanaMinB', 'Noto Sans CJK SC', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'SimSun', serif;
}

body {
  font-family: 'Public Sans', 'Noto Sans SC', sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  margin: 0;
  padding: 0;
}

.chinese-char {
  font-family: var(--chinese-font-family);
  font-size: 1.75rem;
  line-height: 2.25rem;
}

.chinese-char-select {
  font-family: var(--chinese-font-family);
  font-size: 1.25rem;
  line-height: 1.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

#app {
  min-height: 100vh;
}

.placeholder-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}
</style> 