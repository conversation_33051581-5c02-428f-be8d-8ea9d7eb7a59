<template>
  <div class="guangyun-dashboard">
    <!-- 广韵校对核心指标区域 -->
    <div class="guangyun-core-section">
      <el-row :gutter="24">
        <el-col :xs="24" :md="8">
          <el-card class="core-metric-card guangyun-progress">
            <div class="metric-header">
              <el-icon class="metric-icon"><Edit /></el-icon>
              <span class="metric-title">广韵校对总进度</span>
            </div>
            <div class="metric-content">
              <div class="progress-numbers">
                <span class="current">{{ guangyunProgress.completed }}</span>
                <span class="separator">/</span>
                <span class="total">{{ guangyunProgress.total.toLocaleString() }}</span>
              </div>
              <el-progress
                :percentage="guangyunProgress.percentage"
                :stroke-width="8"
                color="#409EFF"
                class="progress-bar"
              />
              <div class="progress-info">
                <span class="percentage">{{ guangyunProgress.percentage.toFixed(1) }}%</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :md="8">
          <el-card class="core-metric-card conflict-resolution">
            <div class="metric-header">
              <el-icon class="metric-icon"><Warning /></el-icon>
              <span class="metric-title">冲突解决状态</span>
            </div>
            <div class="metric-content">
              <div class="conflict-stats">
                <div class="resolved">
                  <span class="number">{{ conflictData.resolved }}</span>
                  <span class="label">已解决</span>
                </div>
                <div class="pending">
                  <span class="number">{{ conflictData.pending }}</span>
                  <span class="label">待解决</span>
                </div>
              </div>
              <el-progress
                :percentage="conflictData.resolutionRate"
                :stroke-width="6"
                color="#67C23A"
                class="resolution-progress"
              />

            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :md="8">
          <el-card class="core-metric-card ref-association">
            <div class="metric-header">
              <el-icon class="metric-icon"><Link /></el-icon>
              <span class="metric-title">ref关联完成度</span>
            </div>
            <div class="metric-content">
              <div class="association-stats">
                <div class="associated">
                  <span class="number">{{ refAssociation.associated.toLocaleString() }}</span>
                  <span class="label">已关联</span>
                </div>
                <div class="missing">
                  <span class="number">{{ refAssociation.missing.toLocaleString() }}</span>
                  <span class="label">未关联</span>
                </div>
              </div>
              <el-progress
                :percentage="refAssociation.rate"
                :stroke-width="6"
                color="#E6A23C"
                class="association-progress"
              />

            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 待解决冲突列表区域 -->
    <div class="conflicts-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Warning /></el-icon>
            <span>待解决冲突列表</span>
            <el-button link size="small" @click="refreshConflicts">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
        </template>

        <!-- 冲突列表 -->
        <div v-loading="conflictsLoading">
          <el-table
            :data="pendingConflicts"
            style="width: 100%"
            :empty-text="pendingConflicts.length === 0 ? '暂无待解决冲突' : ''"
          >
            <el-table-column prop="hanzi" label="汉字" width="80" align="center">
              <template #default="scope">
                <span class="conflict-hanzi">{{ scope.row.hanzi }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="unicode" label="Unicode" width="100">
              <template #default="scope">
                <code class="unicode-code">U+{{ scope.row.unicode }}</code>
              </template>
            </el-table-column>
            <el-table-column prop="fan_qie" label="反切" width="120">
              <template #default="scope">
                <span class="fan-qie">{{ scope.row.fan_qie || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="field_display_name" label="冲突字段" width="90">
              <template #default="scope">
                <el-tag size="small" type="warning">{{ scope.row.field_display_name }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="冲突值" min-width="220">
              <template #default="scope">
                <div class="conflict-values-compact">
                  <span v-if="scope.row.xxt_value" class="value-compact">
                    <span class="source-tag xxt">小</span>{{ scope.row.xxt_value }}
                  </span>
                  <span v-if="scope.row.qx_value" class="value-compact">
                    <span class="source-tag qx">切</span>{{ scope.row.qx_value }}
                  </span>
                  <span v-if="scope.row.yd_value" class="value-compact">
                    <span class="source-tag yd">韵</span>{{ scope.row.yd_value }}
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="merged_value" label="合并结果" width="90">
              <template #default="scope">
                <span class="merged-value">{{ scope.row.merged_value || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="160">
              <template #default="scope">
                <span class="create-time">{{ formatTime(scope.row.created_at) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="scope">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleResolveConflict(scope.row)"
                >
                  解决
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <div class="pagination-wrapper" v-if="conflictTotal > 0">
            <el-pagination
              v-model:current-page="conflictCurrentPage"
              v-model:page-size="conflictPageSize"
              :page-sizes="[10, 20, 50]"
              :total="conflictTotal"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleConflictSizeChange"
              @current-change="handleConflictPageChange"
            />
          </div>
        </div>
      </el-card>
    </div>


  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import {
  Edit, Warning, Link, Refresh
} from '@element-plus/icons-vue'
import { dashboardApi, dashboardUtils } from '@/api/dashboard'
import { conflictApi } from '@/api/conflict'
import { ElMessage } from 'element-plus'

// 响应式数据
const loading = ref(false)
const overviewData = ref(null)

// 冲突相关数据
const conflictsLoading = ref(false)
const pendingConflicts = ref([])
const conflictCurrentPage = ref(1)
const conflictPageSize = ref(10)
const conflictTotal = ref(0)







// 计算属性
const guangyunProgress = computed(() => {
  const completed = overviewData.value?.guangyun_core_metrics.total_progress.completed || 0
  const total = overviewData.value?.guangyun_core_metrics.total_progress.total_records || 0
  const percentage = total > 0 ? (completed / total) * 100 : 0

  return {
    completed,
    total,
    percentage: Math.round(percentage * 10) / 10 // 保留1位小数
  }
})

const conflictData = computed(() => ({
  total: overviewData.value?.guangyun_core_metrics.conflict_resolution.total_conflicts || 0,
  resolved: overviewData.value?.guangyun_core_metrics.conflict_resolution.resolved_conflicts || 0,
  pending: overviewData.value?.guangyun_core_metrics.conflict_resolution.pending_conflicts || 0,
  resolutionRate: overviewData.value?.guangyun_core_metrics.conflict_resolution.resolution_rate || 0
}))



const refAssociation = computed(() => ({
  associated: overviewData.value?.guangyun_core_metrics.ref_association.associated_records || 0,
  total: overviewData.value?.guangyun_core_metrics.ref_association.total_records || 0,
  missing: overviewData.value?.guangyun_core_metrics.ref_association.missing_refs || 0,
  rate: overviewData.value?.guangyun_core_metrics.ref_association.association_rate || 0
}))







// 方法
const fetchOverviewData = async () => {
  loading.value = true
  try {
    // 获取概览数据
    const overviewResponse = await dashboardApi.getOverview()
    if (overviewResponse.success) {
      overviewData.value = overviewResponse.data
    }
  } catch (error) {
    console.error('获取概览数据失败:', error)
    ElMessage.error('获取数据失败: ' + error.message)
    // 清空数据，显示错误状态
    overviewData.value = null
  } finally {
    loading.value = false
  }
}

// 冲突相关方法
const fetchConflictsList = async () => {
  conflictsLoading.value = true
  try {
    const skip = (conflictCurrentPage.value - 1) * conflictPageSize.value

    // 调用真实的API获取冲突数据
    const response = await conflictApi.getConflictsList({
      skip,
      limit: conflictPageSize.value,
      status: 'unresolved' // 只获取待解决的冲突
    })

    if (response && Array.isArray(response)) {
      pendingConflicts.value = response

      // 如果返回的数据少于请求的数量，说明已经是最后一页
      if (response.length < conflictPageSize.value) {
        conflictTotal.value = skip + response.length
      } else {
        // 需要获取总数，这里先估算，建议后端API返回总数
        // 可以通过单独的统计API获取准确总数
        try {
          const statsResponse = await conflictApi.getStatistics()
          if (statsResponse && statsResponse.unresolved_conflicts) {
            conflictTotal.value = statsResponse.unresolved_conflicts
          } else {
            conflictTotal.value = Math.max(conflictTotal.value, skip + response.length + 1)
          }
        } catch (statsError) {
          console.warn('获取冲突统计失败，使用估算值:', statsError)
          conflictTotal.value = Math.max(conflictTotal.value, skip + response.length + 1)
        }
      }
    } else {
      pendingConflicts.value = []
      conflictTotal.value = 0
    }
  } catch (error) {
    console.error('获取冲突列表失败:', error)
    ElMessage.error('获取冲突列表失败: ' + error.message)
    pendingConflicts.value = []
    conflictTotal.value = 0
  } finally {
    conflictsLoading.value = false
  }
}

const refreshConflicts = async () => {
  await fetchConflictsList()
  ElMessage.success('冲突列表已刷新')
}

const handleConflictPageChange = (page) => {
  conflictCurrentPage.value = page
  fetchConflictsList()
}

const handleConflictSizeChange = (size) => {
  conflictPageSize.value = size
  conflictCurrentPage.value = 1
  fetchConflictsList()
}

const handleResolveConflict = (conflict) => {
  // 跳转到广韵校对页面并显示对应的记录
  // 发送自定义事件到App.vue，切换到广韵校对页面并搜索对应汉字
  window.dispatchEvent(new CustomEvent('navigate-to-guangyun', {
    detail: {
      hanzi: conflict.hanzi,
      unicode: conflict.unicode,
      fanQie: conflict.fan_qie
    }
  }))

  ElMessage.success(`正在跳转到广韵校对页面查看"${conflict.hanzi}"的记录`)
}

const formatTime = (timestamp) => {
  return dashboardUtils.formatTime(timestamp)
}

// 生命周期
onMounted(() => {
  fetchOverviewData()
  fetchConflictsList()
})
</script>

<style scoped>
.guangyun-dashboard {
  padding: 24px;
  background-color: #f8fafc;
}

.guangyun-core-section {
  margin-bottom: 24px;
}

.core-metric-card {
  height: 200px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.guangyun-progress {
  border-left: 4px solid #409EFF;
}

.conflict-resolution {
  border-left: 4px solid #67C23A;
}

.ref-association {
  border-left: 4px solid #E6A23C;
}

.metric-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.metric-icon {
  margin-right: 8px;
  font-size: 20px;
}

.metric-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.metric-content {
  flex: 1;
}

.progress-numbers {
  text-align: center;
  margin-bottom: 12px;
}

.current {
  font-size: 32px;
  font-weight: bold;
  color: #409EFF;
}

.separator {
  font-size: 24px;
  color: #909399;
  margin: 0 8px;
}

.total {
  font-size: 18px;
  color: #606266;
}

.progress-bar {
  margin-bottom: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

.conflict-stats,
.association-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16px;
}

.resolved,
.pending,
.associated,
.missing {
  text-align: center;
}

.resolved .number,
.associated .number {
  color: #67C23A;
}

.pending .number,
.missing .number {
  color: #F56C6C;
}

.number {
  display: block;
  font-size: 24px;
  font-weight: bold;
}

.label {
  font-size: 12px;
  color: #909399;
}









.card-header {
  display: flex;
  align-items: center;
}

.card-header .el-icon {
  margin-right: 8px;
}

.no-conflicts {
  text-align: center;
  padding: 40px 0;
}

/* 冲突列表样式 */
.conflicts-section {
  margin-bottom: 24px;
}

.conflict-hanzi {
  font-family: var(--chinese-font-family);
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.unicode-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  background-color: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
  color: #606266;
}

.fan-qie {
  font-family: var(--chinese-font-family);
  color: #606266;
}

.conflict-values-compact {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.value-compact {
  display: inline-flex;
  align-items: center;
  font-size: 12px;
  background-color: #f8fafc;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.source-tag {
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  font-size: 10px;
  font-weight: bold;
  color: white;
  border-radius: 2px;
  margin-right: 4px;
  flex-shrink: 0;
}

.source-tag.xxt {
  background-color: #409EFF;
}

.source-tag.qx {
  background-color: #67C23A;
}

.source-tag.yd {
  background-color: #E6A23C;
}

.merged-value {
  font-family: var(--chinese-font-family);
  color: #67c23a;
  font-weight: bold;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .dashboard-overview {
    padding: 16px;
  }

  .conflict-values-compact {
    gap: 4px;
  }

  .value-compact {
    font-size: 11px;
    padding: 1px 4px;
  }

  .source-tag {
    width: 14px;
    height: 14px;
    line-height: 14px;
    font-size: 9px;
    margin-right: 3px;
  }
}
</style>
