#!/usr/bin/env python3
"""
merge_all_equal.py - 广韵数据合并脚本

功能：
1. 从yunshu_gy_origin按order_num顺序获取数据（仅处理ref为空的记录）
2. 按unicode分组，再按fan_qie分组（支持反切字形转换）
3. 仅处理包含所有3个源(xxt、qx、yd)的分组
4. 合并数据存入yunshu_guangyun表
5. 更新原始记录的ref字段指向新记录

新增功能：
- 反切值字形转换：读取配置文件进行字形转换（如：吕→呂）
- 反切完整转换：在字形转换后，使用fanqie_full_mapping.json进行完整反切转换
- 汉字字形转换：对yd和qx源的汉字进行字形转换（使用zixing_mapping.json）
- 仅处理ref为空的origin记录，避免重复处理已生成guangyun记录的数据
- 支持自定义反切映射文件路径、反切完整映射文件路径和字形映射文件路径

字形转换模式：
- 使用 --enable-zixing-conversion 启用字形转换模式
- 先取yunshu_gy_origin中所有ref为空的数据
- 按反切值分组
- 将source为yd、qx中的汉字用zixing_mapping.json中的汉字对进行转换
- 然后再将3个源都有的字形和反切音进行合并

作者：AI Assistant
日期：2025-08-04
"""

import sys
import os
import logging
import argparse
import json
from pathlib import Path
from typing import Dict, List, Set, Optional, Tuple
from collections import defaultdict
from tqdm import tqdm

# 添加backend目录到路径，以便导入app模块
backend_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.orm import Session
from sqlalchemy import create_engine, text
from app.database import SessionLocal, DATABASE_URL
from app import models, schemas, crud

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('all_equal.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class GuangyunDataProcessor:
    """广韵数据处理器"""

    def __init__(self, dry_run: bool = False, batch_size: int = 1000, fanqie_mapping_file: Optional[str] = None,
                 zixing_mapping_file: Optional[str] = None, enable_zixing_conversion: bool = False,
                 fanqie_full_mapping_file: Optional[str] = None):
        self.dry_run = dry_run
        self.batch_size = batch_size
        self.db = SessionLocal()
        self.fanqie_mapping = self.load_fanqie_mapping(fanqie_mapping_file)
        self.fanqie_full_mapping = self.load_fanqie_full_mapping(fanqie_full_mapping_file)
        self.zixing_mapping = self.load_zixing_mapping(zixing_mapping_file) if enable_zixing_conversion else {}
        self.enable_zixing_conversion = enable_zixing_conversion
        self.stats = {
            'total_unicode_processed': 0,
            'total_groups_found': 0,
            'total_groups_with_all_sources': 0,
            'total_records_created': 0,
            'total_conflicts': 0,
            'source_distribution': {'xxt': 0, 'qx': 0, 'yd': 0},
            'skipped_groups': 0,
            'fanqie_conversions': 0,  # 反切转换次数统计
            'fanqie_full_conversions': 0,  # 新增：反切完整转换次数统计
            'zixing_conversions': 0   # 新增：字形转换次数统计
        }
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.db.close()

    def load_fanqie_mapping(self, mapping_file: Optional[str]) -> Dict[str, str]:
        """加载反切字形转换映射文件"""
        if not mapping_file:
            # 默认映射文件路径
            mapping_file = Path(__file__).parent.parent / "config" / "fanqie_mapping.json"

        mapping_path = Path(mapping_file)
        if not mapping_path.exists():
            logger.warning(f"反切映射文件不存在: {mapping_path}")
            return {}

        try:
            with open(mapping_path, 'r', encoding='utf-8') as f:
                mapping = json.load(f)
            logger.info(f"成功加载反切映射文件: {mapping_path}, 包含 {len(mapping)} 个映射")
            return mapping
        except Exception as e:
            logger.error(f"加载反切映射文件失败: {e}")
            return {}

    def load_fanqie_full_mapping(self, mapping_file: Optional[str]) -> Dict[str, str]:
        """加载反切完整转换映射文件"""
        if not mapping_file:
            # 默认映射文件路径
            mapping_file = Path(__file__).parent.parent / "config" / "fanqie_full_mapping.json"

        mapping_path = Path(mapping_file)
        if not mapping_path.exists():
            logger.warning(f"反切完整映射文件不存在: {mapping_path}")
            return {}

        try:
            with open(mapping_path, 'r', encoding='utf-8') as f:
                mapping = json.load(f)
            logger.info(f"成功加载反切完整映射文件: {mapping_path}, 包含 {len(mapping)} 个映射")
            return mapping
        except Exception as e:
            logger.error(f"加载反切完整映射文件失败: {e}")
            return {}

    def load_zixing_mapping(self, mapping_file: Optional[str]) -> Dict[str, str]:
        """加载字形转换映射文件"""
        if not mapping_file:
            # 默认映射文件路径
            mapping_file = Path(__file__).parent.parent / "config" / "zixing_mapping.json"

        mapping_path = Path(mapping_file)
        if not mapping_path.exists():
            logger.warning(f"字形映射文件不存在: {mapping_path}")
            return {}

        try:
            with open(mapping_path, 'r', encoding='utf-8') as f:
                mapping = json.load(f)
            logger.info(f"成功加载字形映射文件: {mapping_path}, 包含 {len(mapping)} 个映射")
            return mapping
        except Exception as e:
            logger.error(f"加载字形映射文件失败: {e}")
            return {}

    def normalize_fanqie(self, fanqie: str) -> str:
        """标准化反切：如果不以"切"结尾，则补上"切"字"""
        if not fanqie:
            return fanqie

        # 如果不以"切"结尾，则补上"切"字
        if not fanqie.endswith('切'):
            normalized = fanqie + '切'
            logger.debug(f"标准化反切: '{fanqie}' -> '{normalized}'")
            return normalized

        return fanqie

    def convert_fanqie_characters(self, fanqie: str) -> str:
        """转换反切值中的字符，包括标准化处理和完整转换"""
        if not fanqie:
            return fanqie

        # 首先进行标准化处理（补"切"字）
        normalized_fanqie = self.normalize_fanqie(fanqie)

        # 第一步：使用fanqie_mapping进行字符级转换
        converted = normalized_fanqie
        original_fanqie = fanqie

        if self.fanqie_mapping:
            # 逐字符转换
            for old_char, new_char in self.fanqie_mapping.items():
                if old_char in converted:
                    converted = converted.replace(old_char, new_char)

            # 如果发生了转换，记录统计
            if converted != original_fanqie:
                self.stats['fanqie_conversions'] += 1
                logger.debug(f"反切字符转换: {original_fanqie} -> {converted}")

        # 第二步：使用fanqie_full_mapping进行完整转换
        if self.fanqie_full_mapping and converted in self.fanqie_full_mapping:
            full_converted = self.fanqie_full_mapping[converted]
            if full_converted != converted:
                self.stats['fanqie_full_conversions'] += 1
                logger.debug(f"反切完整转换: {converted} -> {full_converted}")
                converted = full_converted

        return converted

    def convert_hanzi_character(self, hanzi: str) -> str:
        """转换汉字字形"""
        if not hanzi or not self.zixing_mapping:
            return hanzi

        # 如果汉字在映射表中，进行转换
        if hanzi in self.zixing_mapping:
            converted = self.zixing_mapping[hanzi]
            self.stats['zixing_conversions'] += 1
            logger.debug(f"字形转换: {hanzi} -> {converted}")
            return converted

        return hanzi

    def get_ordered_unicode_list(self, start_order: int = 1, limit: Optional[int] = None) -> List[str]:
        """获取按order_num排序的unicode列表"""
        # 修复MySQL DISTINCT + ORDER BY 问题：需要在子查询中先排序，再去重
        query = """
        SELECT unicode
        FROM (
            SELECT unicode, MIN(order_num) as min_order
            FROM yunshu_gy_origin
            WHERE order_num IS NOT NULL AND order_num >= :start_order
            GROUP BY unicode
            ORDER BY min_order ASC
        ) AS ordered_unicode
        """

        if limit:
            query += f" LIMIT {limit}"

        result = self.db.execute(text(query), {"start_order": start_order})
        unicode_list = [row[0] for row in result.fetchall()]

        logger.info(f"找到 {len(unicode_list)} 个unicode待处理")
        return unicode_list
    
    def get_records_by_unicode(self, unicode_code: str) -> List[models.YinyunGyOrigin]:
        """获取指定unicode的所有记录，仅处理ref为空的记录"""
        records = self.db.query(models.YinyunGyOrigin).filter(
            models.YinyunGyOrigin.unicode == unicode_code,
            models.YinyunGyOrigin.ref.is_(None)  # 只处理ref为空的记录
        ).order_by(
            # 按来源优先级排序
            text("CASE source WHEN 'xxt' THEN 1 WHEN 'qx' THEN 2 WHEN 'yd' THEN 3 END")
        ).all()

        # 如果启用了字形转换，对yd和qx源的汉字进行转换
        if self.enable_zixing_conversion:
            for record in records:
                if record.source in ['yd', 'qx']:
                    original_hanzi = record.hanzi
                    converted_hanzi = self.convert_hanzi_character(record.hanzi)
                    if converted_hanzi != original_hanzi:
                        record.hanzi = converted_hanzi
                        logger.debug(f"源 {record.source} 汉字转换: {original_hanzi} -> {converted_hanzi}")

        return records
    
    def group_by_fan_qie(self, records: List[models.YinyunGyOrigin]) -> Dict[str, List[models.YinyunGyOrigin]]:
        """按反切值分组，应用字形转换。只处理有反切值的记录"""
        groups = defaultdict(list)
        for record in records:
            # 只处理有反切值的记录，空值或空字符串都跳过
            if record.fan_qie and record.fan_qie.strip():
                # 应用字形转换
                converted_fanqie = self.convert_fanqie_characters(record.fan_qie)
                groups[converted_fanqie].append(record)
        return dict(groups)

    def has_all_three_sources(self, records: List[models.YinyunGyOrigin]) -> bool:
        """检查记录组是否包含所有3个源（宽松检查）"""
        sources = set(record.source for record in records)
        return {'xxt', 'qx', 'yd'}.issubset(sources)

    def is_strict_three_source_singleton_group(self, records: List[models.YinyunGyOrigin]) -> bool:
        """严格检查：必须正好3条记录，且来源恰为{xxt, qx, yd}各一条"""
        if len(records) != 3:
            return False
        sources = [r.source for r in records]
        return set(sources) == {'xxt', 'qx', 'yd'} and all(sources.count(s) == 1 for s in ['xxt', 'qx', 'yd'])

    def get_field_value(self, sources: Dict[str, List[models.YinyunGyOrigin]],
                       field_name: str, priority_order: List[str]) -> Optional[str]:
        """根据优先级获取字段值"""
        for source in priority_order:
            if source in sources and sources[source]:
                for record in sources[source]:
                    value = getattr(record, field_name, None)
                    if value is not None and str(value).strip():
                        return str(value).strip()
        return None
    
    def detect_conflicts(self, sources: Dict[str, List[models.YinyunGyOrigin]],
                        field_name: str) -> bool:
        """
        检测字段冲突
        规则：
        1. 忽略空值（None或空字符串）
        2. 只有当有值的字段之间不同时才记为冲突
        3. 如果只有一个源有值，不算冲突
        """
        non_empty_values = set()

        for source_records in sources.values():
            for record in source_records:
                value = getattr(record, field_name, None)
                # 只考虑非空值
                if value is not None and str(value).strip():
                    non_empty_values.add(str(value).strip())

        # 只有当有2个或以上不同的非空值时才算冲突
        return len(non_empty_values) > 1

    def get_field_conflict_details(self, sources: Dict[str, List[models.YinyunGyOrigin]],
                                  field_name: str) -> Dict[str, str]:
        """
        获取字段冲突详情，用于调试
        返回每个源的字段值
        """
        field_values = {}

        for source, source_records in sources.items():
            for record in source_records:
                value = getattr(record, field_name, None)
                if value is not None and str(value).strip():
                    field_values[source] = str(value).strip()
                else:
                    field_values[source] = "(空值)"

        return field_values

    def create_conflict_record(self, unicode_code: str, hanzi: str, guangyun_id: int,
                              fan_qie: str, field_name: str, field_display_name: str,
                              sources: Dict[str, List[models.YinyunGyOrigin]],
                              merged_value: str, merge_rule: str):
        """创建冲突记录"""
        if self.dry_run:
            return

        try:
            # 获取各源的值
            xxt_value = None
            qx_value = None
            yd_value = None

            for source, source_records in sources.items():
                for record in source_records:
                    value = getattr(record, field_name, None)
                    if value is not None and str(value).strip():
                        if source == 'xxt':
                            xxt_value = str(value).strip()
                        elif source == 'qx':
                            qx_value = str(value).strip()
                        elif source == 'yd':
                            yd_value = str(value).strip()

            # 创建冲突记录
            conflict_data = schemas.YunshuConflictRecordCreate(
                unicode=unicode_code,
                hanzi=hanzi,
                guangyun_id=guangyun_id,
                fan_qie=fan_qie,
                field_name=field_name,
                field_display_name=field_display_name,
                xxt_value=xxt_value,
                qx_value=qx_value,
                yd_value=yd_value,
                merged_value=merged_value,
                merge_rule=merge_rule,
                conflict_status='unresolved'
            )

            crud.conflict_record_crud.create_conflict_record(self.db, conflict_data)

        except Exception as e:
            logger.error(f"创建冲突记录失败: {e}")

    def merge_records(self, records: List[models.YinyunGyOrigin], converted_fan_qie: str) -> Tuple[schemas.YunshuGuangyunCreate, int]:
        """合并同一反切值的多条记录"""
        # 按来源分类
        sources = {'xxt': [], 'qx': [], 'yd': []}
        for record in records:
            if record.source in sources:
                sources[record.source].append(record)

        # 统计各来源记录数
        for source, source_records in sources.items():
            self.stats['source_distribution'][source] += len(source_records)

        # 基础字段（取第一条记录）
        first_record = records[0]
        unicode_code = first_record.unicode
        hanzi = first_record.hanzi

        # 音韵字段合并规则
        default_priority = ['xxt', 'qx', 'yd']
        special_priority = ['qx', 'xxt', 'yd']  # shi_yi和xiao_yun的特殊优先级

        # 合并各字段
        merged_data = {
            'unicode': unicode_code,
            'hanzi': hanzi,
            'fan_qie': converted_fan_qie,  # 使用转换后的标准反切值
            'sheng_mu': self.get_field_value(sources, 'sheng_mu', default_priority),
            'yun_bu': self.get_field_value(sources, 'yun_bu', default_priority),
            'sheng_diao': self.get_field_value(sources, 'sheng_diao', default_priority),
            'kai_he': self.get_field_value(sources, 'kai_he', default_priority),
            'deng_di': self.get_field_value(sources, 'deng_di', default_priority),
            'she': self.get_field_value(sources, 'she', default_priority),
            'qing_zhuo': self.get_field_value(sources, 'qing_zhuo', default_priority),
            # 特殊字段：shi_yi和xiao_yun以qx为准
            'shi_yi': self.get_field_value(sources, 'shi_yi', special_priority),
            'xiao_yun': self.get_field_value(sources, 'xiao_yun', special_priority),
        }
        
        # 检测冲突（shi_yi字段不参与冲突比较）
        conflict_fields = [
            'fan_qie', 'sheng_mu', 'yun_bu', 'sheng_diao', 'kai_he',
            'deng_di', 'she', 'qing_zhuo', 'xiao_yun'
        ]

        conflicts = 0
        conflict_details = []
        conflict_fields_info = []  # 存储冲突字段的详细信息

        # 字段显示名称映射
        field_display_names = {
            'fan_qie': '反切',
            'sheng_mu': '声母',
            'yun_bu': '韵部',
            'sheng_diao': '声调',
            'kai_he': '开合',
            'deng_di': '等第',
            'she': '摄',
            'qing_zhuo': '清浊',
            'xiao_yun': '小韵'
        }

        for field in conflict_fields:
            if self.detect_conflicts(sources, field):
                conflicts += 1
                # 获取冲突详情
                field_values = self.get_field_conflict_details(sources, field)
                conflict_details.append(f"{field}: {field_values}")

                # 保存冲突字段信息，用于后续创建冲突记录
                merge_rule = "xxt优先" if field != 'shi_yi' and field != 'xiao_yun' else "qx优先"
                conflict_fields_info.append({
                    'field_name': field,
                    'field_display_name': field_display_names.get(field, field),
                    'merged_value': merged_data.get(field),
                    'merge_rule': merge_rule
                })

        merged_data['conflicts'] = conflicts
        self.stats['total_conflicts'] += conflicts

        return schemas.YunshuGuangyunCreate(**merged_data), conflicts, conflict_fields_info, sources
    
    def save_to_guangyun(self, merged_record: schemas.YunshuGuangyunCreate) -> Optional[int]:
        """保存合并后的记录到yunshu_guangyun表"""
        if self.dry_run:
            return 999999  # 返回模拟ID

        try:
            # 检查是否已存在相同的记录
            existing = self.db.query(models.YunshuGuangyun).filter(
                models.YunshuGuangyun.unicode == merged_record.unicode,
                models.YunshuGuangyun.fan_qie == merged_record.fan_qie
            ).first()

            if existing:
                return existing.id

            # 创建新记录
            db_record = crud.yunshu_guangyun_crud.create(self.db, merged_record)
            self.stats['total_records_created'] += 1

            return db_record.id

        except Exception as e:
            logger.error(f"保存记录失败: {e}")
            return None
    
    def update_origin_refs(self, records: List[models.YinyunGyOrigin], guangyun_id: int):
        """更新原始记录的ref字段"""
        if self.dry_run:
            return

        try:
            for record in records:
                record.ref = guangyun_id
            self.db.commit()
        except Exception as e:
            logger.error(f"更新ref字段失败: {e}")
            self.db.rollback()
    
    def process_unicode_data(self, unicode_code: str):
        """处理单个unicode的数据"""
        # 获取该unicode的所有记录
        records = self.get_records_by_unicode(unicode_code)
        if not records:
            return

        # 首先检查是否包含所有3个源
        if not self.has_all_three_sources(records):
            return

        # 按fan_qie分组
        groups = self.group_by_fan_qie(records)
        self.stats['total_groups_found'] += len(groups)

        # 处理每个分组
        for fan_qie, group_records in groups.items():
            # 再次检查分组是否包含所有3个源（因为按反切分组后可能会分散）
            if not self.has_all_three_sources(group_records):
                self.stats['skipped_groups'] += 1
                continue

            # 严格检查：必须正好3个源且每源仅一条
            if not self.is_strict_three_source_singleton_group(group_records):
                logger.info(f"跳过分组(不满足严格3源单条)：fan_qie={fan_qie}，记录数={len(group_records)}，sources={[r.source for r in group_records]}")
                self.stats['skipped_groups'] += 1
                continue

            self.stats['total_groups_with_all_sources'] += 1

            # 合并记录（传递转换后的反切值）
            merged_record, conflicts, conflict_fields_info, sources = self.merge_records(group_records, fan_qie)

            # 保存到目标表
            guangyun_id = self.save_to_guangyun(merged_record)

            if guangyun_id:
                # 更新原始记录的ref字段
                self.update_origin_refs(group_records, guangyun_id)

                # 创建冲突记录
                if conflicts > 0:
                    # 为每个冲突字段创建冲突记录
                    for conflict_info in conflict_fields_info:
                        self.create_conflict_record(
                            unicode_code=unicode_code,
                            hanzi=merged_record.hanzi,
                            guangyun_id=guangyun_id,
                            fan_qie=fan_qie,
                            field_name=conflict_info['field_name'],
                            field_display_name=conflict_info['field_display_name'],
                            sources=sources,
                            merged_value=conflict_info['merged_value'],
                            merge_rule=conflict_info['merge_rule']
                        )
    
    def process_all_data(self, start_order: int = 1, limit: Optional[int] = None):
        """处理所有数据"""
        logger.info("开始处理广韵数据...")

        if self.dry_run:
            logger.info("*** 试运行模式 - 不会实际修改数据库 ***")

        if self.enable_zixing_conversion:
            # 如果启用字形转换，使用新的处理逻辑（不需要start_order参数）
            self.process_all_data_with_zixing_conversion(limit)
        else:
            # 原有的按unicode处理逻辑
            self.process_all_data_by_unicode(start_order, limit)

        # 输出统计信息
        self.print_statistics()

    def process_all_data_by_unicode(self, start_order: int = 1, limit: Optional[int] = None):
        """按unicode处理数据（原有逻辑）"""
        # 获取unicode列表
        unicode_list = self.get_ordered_unicode_list(start_order, limit)

        if not unicode_list:
            logger.warning("没有找到需要处理的数据")
            return

        # 处理每个unicode
        for unicode_code in tqdm(unicode_list, desc="处理进度"):
            try:
                self.process_unicode_data(unicode_code)
                self.stats['total_unicode_processed'] += 1
            except Exception as e:
                logger.error(f"处理Unicode {unicode_code} 时出错: {e}")
                continue
    
    def print_statistics(self):
        """输出处理统计信息"""
        logger.info("=" * 50)
        logger.info("处理统计信息:")
        logger.info(f"处理的Unicode数量: {self.stats['total_unicode_processed']}")
        logger.info(f"找到的分组总数: {self.stats['total_groups_found']}")
        logger.info(f"包含所有3个源的分组: {self.stats['total_groups_with_all_sources']}")
        logger.info(f"跳过的分组数: {self.stats['skipped_groups']}")
        logger.info(f"创建的记录数: {self.stats['total_records_created']}")
        logger.info(f"总冲突数: {self.stats['total_conflicts']}")
        logger.info(f"反切字符转换次数: {self.stats['fanqie_conversions']}")
        logger.info(f"反切完整转换次数: {self.stats['fanqie_full_conversions']}")
        if self.enable_zixing_conversion:
            logger.info(f"字形转换次数: {self.stats['zixing_conversions']}")
        logger.info("来源分布:")
        for source, count in self.stats['source_distribution'].items():
            logger.info(f"  {source}: {count}")
        logger.info("=" * 50)

    def process_all_data_with_zixing_conversion(self, limit: Optional[int] = None):
        """启用字形转换时的处理逻辑 - 一次性获取所有ref为空的记录"""
        # 获取所有记录（字形转换模式不需要start_order参数）
        all_records = self.get_all_records_for_zixing_conversion(limit)

        if not all_records:
            logger.warning("没有找到需要处理的数据")
            return

        logger.info(f"找到 {len(all_records)} 条记录待处理")

        # 应用字形转换并按转换后的汉字分组
        hanzi_groups = self.group_records_by_converted_hanzi(all_records)

        logger.info(f"字形转换后得到 {len(hanzi_groups)} 个汉字分组")

        # 处理每个汉字分组
        for hanzi, records in tqdm(hanzi_groups.items(), desc="处理进度"):
            try:
                self.process_hanzi_group(hanzi, records)
            except Exception as e:
                logger.error(f"处理汉字分组 {hanzi} 时出错: {e}")
                continue

    def get_all_records_for_zixing_conversion(self, limit: Optional[int] = None):
        """获取所有ref为空的记录用于字形转换处理"""
        query = self.db.query(models.YinyunGyOrigin).filter(
            models.YinyunGyOrigin.ref.is_(None)  # 只处理ref为空的记录
        ).order_by(models.YinyunGyOrigin.id)  # 简单按id排序即可

        if limit:
            query = query.limit(limit)

        return query.all()

    def get_all_records_ordered(self, start_order: int = 1, limit: Optional[int] = None):
        """获取所有记录，按order_num排序（包含order_num为None的记录）"""
        from sqlalchemy import or_, case

        query = self.db.query(models.YinyunGyOrigin).filter(
            models.YinyunGyOrigin.ref.is_(None),  # 只处理ref为空的记录
            or_(
                models.YinyunGyOrigin.order_num.is_(None),  # 包含order_num为None的记录
                models.YinyunGyOrigin.order_num >= start_order
            )
        ).order_by(
            # 使用CASE语句将None值排在最后
            case(
                (models.YinyunGyOrigin.order_num.is_(None), 999999999),
                else_=models.YinyunGyOrigin.order_num
            ).asc()
        )

        if limit:
            query = query.limit(limit)

        return query.all()

    def group_records_by_converted_hanzi(self, records):
        """按转换后的汉字分组记录"""
        from collections import defaultdict

        hanzi_groups = defaultdict(list)

        for record in records:
            # 应用字形转换
            converted_hanzi = record.hanzi
            if record.source in ['yd', 'qx']:
                original_hanzi = record.hanzi
                converted_hanzi = self.convert_hanzi_character(record.hanzi)
                if converted_hanzi != original_hanzi:
                    self.stats['zixing_conversions'] += 1
                    logger.debug(f"源 {record.source} 汉字转换: {original_hanzi} -> {converted_hanzi}")

            # 按转换后的汉字分组
            hanzi_groups[converted_hanzi].append(record)

        return dict(hanzi_groups)

    def process_hanzi_group(self, hanzi: str, records):
        """处理单个汉字分组"""
        # 按反切值分组
        groups = self.group_by_fan_qie(records)
        self.stats['total_groups_found'] += len(groups)

        # 处理每个分组
        for fan_qie, group_records in groups.items():
            # 检查是否包含所有3个源
            if not self.has_all_three_sources(group_records):
                self.stats['skipped_groups'] += 1
                continue

            # 严格检查：必须正好3个源且每源仅一条
            if not self.is_strict_three_source_singleton_group(group_records):
                logger.info(f"跳过分组(不满足严格3源单条)：hanzi={hanzi} fan_qie={fan_qie}，记录数={len(group_records)}，sources={[r.source for r in group_records]}")
                self.stats['skipped_groups'] += 1
                continue

            self.stats['total_groups_with_all_sources'] += 1

            # 合并记录（传递转换后的反切值）
            merged_record, conflicts, conflict_fields_info, sources = self.merge_records(group_records, fan_qie)

            # 保存到目标表
            guangyun_id = self.save_to_guangyun(merged_record)

            if guangyun_id:
                # 更新原始记录的ref字段
                self.update_origin_refs(group_records, guangyun_id)

                # 创建冲突记录
                if conflicts > 0:
                    # 为每个冲突字段创建冲突记录
                    for conflict_info in conflict_fields_info:
                        self.create_conflict_record(
                            unicode_code=merged_record.unicode,
                            hanzi=merged_record.hanzi,
                            guangyun_id=guangyun_id,
                            fan_qie=fan_qie,
                            field_name=conflict_info['field_name'],
                            field_display_name=conflict_info['field_display_name'],
                            sources=sources,
                            merged_value=conflict_info['merged_value'],
                            merge_rule=conflict_info['merge_rule']
                        )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='广韵数据合并脚本')
    parser.add_argument('--dry-run', action='store_true', help='试运行模式，不实际写入数据库')
    parser.add_argument('--batch-size', type=int, default=1000, help='批处理大小')
    parser.add_argument('--start-order', type=int, default=1, help='起始order_num')
    parser.add_argument('--limit', type=int, help='处理记录数限制，用于测试')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--fanqie-mapping', type=str, help='反切字形转换映射文件路径')
    parser.add_argument('--fanqie-full-mapping', type=str, help='反切完整转换映射文件路径')
    parser.add_argument('--zixing-mapping', type=str, help='汉字字形转换映射文件路径')
    parser.add_argument('--enable-zixing-conversion', action='store_true',
                       help='启用字形转换模式（对yd和qx源的汉字进行转换）')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        with GuangyunDataProcessor(
            dry_run=args.dry_run,
            batch_size=args.batch_size,
            fanqie_mapping_file=args.fanqie_mapping,
            zixing_mapping_file=args.zixing_mapping,
            enable_zixing_conversion=args.enable_zixing_conversion,
            fanqie_full_mapping_file=args.fanqie_full_mapping
        ) as processor:
            processor.process_all_data(start_order=args.start_order, limit=args.limit)

        logger.info("处理完成！")

    except KeyboardInterrupt:
        logger.info("用户中断处理")
    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    main()
