#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试sources覆盖验证逻辑
"""

def validate_sources_coverage(sources1, sources2, all_sources_in_group):
    """验证两个汉字的sources合并后是否能覆盖组内所有来源"""
    combined_sources = set(sources1) | set(sources2)
    return combined_sources == all_sources_in_group

# 测试"居追切"组的数据
all_sources = {"qx", "xxt", "yd"}

# 测试用例
test_cases = [
    ("𤕣 vs 𠃾", ["yd"], ["qx", "xxt"]),
    ("𤕣 vs 𪚦", ["yd"], ["xxt", "yd"]),
    ("𠃾 vs 𪚦", ["qx", "xxt"], ["xxt", "yd"])
]

print("=== Sources覆盖验证测试 ===")
print(f"组内所有来源: {sorted(all_sources)}")
print()

for name, sources1, sources2 in test_cases:
    result = validate_sources_coverage(sources1, sources2, all_sources)
    combined = set(sources1) | set(sources2)
    print(f"{name}:")
    print(f"  sources1: {sources1}")
    print(f"  sources2: {sources2}")
    print(f"  合并后: {sorted(combined)}")
    print(f"  覆盖完整: {result}")
    print()
