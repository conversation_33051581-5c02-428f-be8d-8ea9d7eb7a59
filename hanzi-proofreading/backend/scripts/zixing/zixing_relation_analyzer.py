#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析null_ref_fanqie_analysis.json文件，建立字形关系映射
1. 读取null_ref_fanqie_analysis.json文件
2. 对于同一个反切值下的汉字进行分组
3. 调用relation-group接口检查同一反切组中的每对汉字是否在同一个关系组中
4. 若两个汉字在同一关系组中，且其中一个包含xxt来源，则建立mapping关系记录到zixing_mapping.json中
5. 若两个汉字在同一关系组中，但都有xxt或都没有xxt，则记录到zixing_to_check.json中
6. 注意两个文件都要去重

作者: AI Assistant
日期: 2025-08-06 (修改版本)
"""

import os
import sys
import json
import logging
import requests
import time
from datetime import datetime
from collections import defaultdict
from typing import Dict, List, Optional, Set, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('zixing_relation_analyzer.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ZixingRelationAnalyzer:
    """字形关系分析器"""
    
    def __init__(self, api_base_url: str = "http://localhost:5173"):
        """初始化分析器
        
        Args:
            api_base_url: API基础URL
        """
        self.api_base_url = api_base_url
        self.zixing_mapping = {}  # 存储映射关系
        self.zixing_to_check = {}  # 存储需要检查的关系
        self.processed_count = 0
        self.total_count = 0
        
    def load_analysis_data(self, input_file: str) -> Optional[Dict]:
        """加载分析数据文件
        
        Args:
            input_file: 输入文件路径
            
        Returns:
            Dict: 分析数据，如果失败返回None
        """
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"成功加载分析数据文件: {input_file}")
            return data
        except Exception as e:
            logger.error(f"加载分析数据文件失败: {e}")
            return None
    
    def get_hanzi_relation_group(self, unicode_code: str) -> Optional[Dict]:
        """调用API获取汉字关系组信息
        
        Args:
            unicode_code: 汉字的Unicode编码
            
        Returns:
            Dict: 关系组信息，如果失败返回None
        """
        try:
            url = f"{self.api_base_url}/api/hanzi/{unicode_code}/relation-group"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"API调用失败，状态码: {response.status_code}, Unicode: {unicode_code}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"API调用异常: {e}, Unicode: {unicode_code}")
            return None
        except Exception as e:
            logger.error(f"处理API响应时发生错误: {e}, Unicode: {unicode_code}")
            return None
    
    def extract_zhengyi_relations(self, relation_data: Dict) -> List[Tuple[str, str]]:
        """从关系数据中提取zhengyi关系的source:target对

        Args:
            relation_data: 关系数据

        Returns:
            List[Tuple[str, str]]: (source_unicode, target_unicode)对的列表
        """
        relations = []

        try:
            # 检查数据结构 - API返回的是直接的字典，不是嵌套在'data'中
            if not relation_data:
                return relations

            # 查找zhengyi_relations字段
            if 'zhengyi_relations' in relation_data:
                zhengyi_relations = relation_data['zhengyi_relations']
                if isinstance(zhengyi_relations, list):
                    for relation in zhengyi_relations:
                        if isinstance(relation, dict):
                            source = relation.get('source_unicode')
                            target = relation.get('target_unicode')
                            if source and target:
                                relations.append((source, target))

        except Exception as e:
            logger.error(f"提取zhengyi关系时发生错误: {e}")

        return relations

    def extract_related_hanzi_unicodes(self, relation_data: Dict) -> List[str]:
        """从关系数据中提取同一关系组中的所有汉字Unicode编码

        Args:
            relation_data: 关系数据

        Returns:
            List[str]: 同一关系组中的汉字Unicode编码列表
        """
        related_unicodes = []

        try:
            # 检查数据结构
            if not relation_data:
                return related_unicodes

            # 查找related_hanzi字段
            if 'related_hanzi' in relation_data:
                related_hanzi = relation_data['related_hanzi']
                if isinstance(related_hanzi, list):
                    for hanzi_info in related_hanzi:
                        if isinstance(hanzi_info, dict):
                            unicode_code = hanzi_info.get('unicode_code')
                            if unicode_code:
                                related_unicodes.append(unicode_code)

        except Exception as e:
            logger.error(f"提取关系组汉字时发生错误: {e}")

        return related_unicodes

    def check_hanzi_in_same_group(self, unicode1: str, unicode2: str) -> bool:
        """检查两个汉字是否在同一个关系组中

        Args:
            unicode1: 第一个汉字的Unicode编码
            unicode2: 第二个汉字的Unicode编码

        Returns:
            bool: 如果两个汉字在同一个关系组中返回True，否则返回False
        """
        try:
            # 获取第一个汉字的关系组
            relation_data = self.get_hanzi_relation_group(unicode1)
            if not relation_data:
                return False

            # 提取关系组中的所有汉字Unicode
            related_unicodes = self.extract_related_hanzi_unicodes(relation_data)

            # 检查第二个汉字是否在关系组中
            return unicode2 in related_unicodes

        except Exception as e:
            logger.error(f"检查汉字关系组时发生错误: {e}")
            return False
    
    def check_target_has_xxt_source(self, target_unicode: str, analysis_data: Dict) -> Optional[str]:
        """检查目标汉字是否包含xxt来源
        
        Args:
            target_unicode: 目标汉字的Unicode编码
            analysis_data: 分析数据
            
        Returns:
            str: 如果找到包含xxt来源的汉字，返回汉字本身，否则返回None
        """
        try:
            groups = analysis_data.get('groups', {})
            
            # 遍历所有组，查找目标Unicode
            for fanqie, group_info in groups.items():
                hanzi_details = group_info.get('hanzi_details', [])
                for hanzi_detail in hanzi_details:
                    if hanzi_detail.get('unicode') == target_unicode:
                        sources = hanzi_detail.get('sources', [])
                        if 'xxt' in sources:
                            return hanzi_detail.get('hanzi', '')
            
            return None
            
        except Exception as e:
            logger.error(f"检查目标汉字xxt来源时发生错误: {e}")
            return None
    
    def validate_sources_coverage(self, sources1: List[str], sources2: List[str], all_sources_in_group: Set[str]) -> bool:
        """验证两个汉字的来源是否可以建立映射关系：
        1) 两个汉字的来源集合必须互不重叠（即交集为空），确保每个来源只出现一次
        2) 两个汉字的来源并集必须是组内来源的子集（不能有组外来源）

        注意：不再要求两个汉字必须覆盖组内所有来源，这样可以处理3个汉字的情况
        """
        s1 = set(sources1)
        s2 = set(sources2)
        combined = s1 | s2

        # 条件1：互不重叠
        if s1 & s2:
            return False

        # 条件2：并集必须是组内来源的子集
        if not combined.issubset(all_sources_in_group):
            return False

        return True

    def find_hanzi_unicode(self, hanzi: str) -> Optional[str]:
        """查找汉字的unicode编码"""
        if not hasattr(self, 'analysis_data') or not hanzi:
            return None

        groups = self.analysis_data.get('groups', {})
        for group_info in groups.values():
            hanzi_details = group_info.get('hanzi_details', [])
            for detail in hanzi_details:
                if detail.get('hanzi') == hanzi:
                    return detail.get('unicode')

        # 如果没找到，使用字符编码
        if len(hanzi) == 1:
            return f"{ord(hanzi):X}"
        return None

    def find_hanzi_sources(self, hanzi: str, fanqie: str) -> List[str]:
        """查找汉字在指定反切组中的sources"""
        if not hasattr(self, 'analysis_data') or not hanzi:
            return []

        groups = self.analysis_data.get('groups', {})
        group_info = groups.get(fanqie, {})
        hanzi_details = group_info.get('hanzi_details', [])

        for detail in hanzi_details:
            if detail.get('hanzi') == hanzi:
                return detail.get('sources', [])

        return []

    def process_fanqie_group(self, fanqie: str, group_info: Dict, analysis_data: Dict):
        """处理单个反切组

        Args:
            fanqie: 反切值
            group_info: 组信息
            analysis_data: 完整的分析数据
        """
        hanzi_details = group_info.get('hanzi_details', [])

        if len(hanzi_details) < 2:
            # 只有一个汉字的组，跳过
            return

        logger.info(f"处理反切组: {fanqie}, 包含 {len(hanzi_details)} 个汉字")

        # 计算组内所有来源的集合
        all_sources_in_group = set()
        for detail in hanzi_details:
            sources = detail.get('sources', [])
            all_sources_in_group.update(sources)
        logger.debug(f"反切组 {fanqie} 的所有来源: {sorted(all_sources_in_group)}")

        # 对同一反切组中的每对汉字进行检查
        for i in range(len(hanzi_details)):
            for j in range(i + 1, len(hanzi_details)):
                hanzi1_detail = hanzi_details[i]
                hanzi2_detail = hanzi_details[j]

                unicode1 = hanzi1_detail.get('unicode')
                unicode2 = hanzi2_detail.get('unicode')
                hanzi1 = hanzi1_detail.get('hanzi')
                hanzi2 = hanzi2_detail.get('hanzi')
                sources1 = hanzi1_detail.get('sources', [])
                sources2 = hanzi2_detail.get('sources', [])

                if not unicode1 or not unicode2:
                    continue

                logger.debug(f"检查汉字对: {hanzi1} ({unicode1}) vs {hanzi2} ({unicode2})")

                # 检查两个汉字是否在同一个关系组中
                if self.check_hanzi_in_same_group(unicode1, unicode2):
                    logger.info(f"发现同组关系: {hanzi1}({unicode1}) <-> {hanzi2}({unicode2})")

                    # 验证sources覆盖性：只有当两个汉字的sources合并后能覆盖组内所有来源时，才建立映射
                    logger.debug(f"验证sources覆盖: {hanzi1}({sources1}) + {hanzi2}({sources2}) vs 组内所有来源: {sorted(all_sources_in_group)}")
                    if not self.validate_sources_coverage(sources1, sources2, all_sources_in_group):
                        logger.info(f"跳过映射: {hanzi1}({sources1}) <-> {hanzi2}({sources2}) - sources覆盖不完整，组内所有来源: {sorted(all_sources_in_group)}")
                        continue
                    logger.debug(f"sources覆盖验证通过: {hanzi1}({sources1}) + {hanzi2}({sources2})")

                    # 确定正确的映射方向：没有xxt的 -> 有xxt的
                    source1_has_xxt = 'xxt' in sources1
                    source2_has_xxt = 'xxt' in sources2

                    # 确定映射方向和处理逻辑
                    if source2_has_xxt and not source1_has_xxt:
                        # hanzi2有xxt，hanzi1没有xxt：hanzi1 -> hanzi2（建立自动映射）
                        map_source_unicode = unicode1
                        map_target_unicode = unicode2
                        map_source_hanzi = hanzi1
                        map_target_hanzi = hanzi2
                        map_source_sources = sources1
                        map_target_sources = sources2
                        logger.info(f"映射方向1: {map_source_hanzi}({map_source_sources}) -> {map_target_hanzi}({map_target_sources})")

                        # 建立自动映射
                        if map_target_hanzi and map_source_hanzi and map_source_hanzi != map_target_hanzi:
                            # 检查是否已存在映射
                            if map_source_hanzi in self.zixing_mapping:
                                existing_target = self.zixing_mapping[map_source_hanzi]

                                # 只有当目标不同时才认为是冲突
                                if existing_target != map_target_hanzi:
                                    logger.warning(f"发现映射冲突: {map_source_hanzi} 已映射到 {existing_target}，现在又要映射到 {map_target_hanzi}")

                                    # 将冲突的映射移到待检查列表
                                    if map_source_hanzi not in self.zixing_to_check:
                                        self.zixing_to_check[map_source_hanzi] = []

                                    # 查找现有目标的unicode和sources
                                    existing_target_unicode = self.find_hanzi_unicode(existing_target)
                                    existing_target_sources = self.find_hanzi_sources(existing_target, fanqie)

                                    # 添加现有映射到待检查列表
                                    self.zixing_to_check[map_source_hanzi].append({
                                        'target_unicode': existing_target_unicode,
                                        'target_hanzi': existing_target,
                                        'target_sources': existing_target_sources,
                                        'source_sources': map_source_sources,
                                        'fanqie': fanqie,
                                        'conflict_reason': 'existing_mapping'
                                    })

                                    # 添加新映射到待检查列表
                                    self.zixing_to_check[map_source_hanzi].append({
                                        'target_unicode': map_target_unicode,
                                        'target_hanzi': map_target_hanzi,
                                        'target_sources': map_target_sources,
                                        'source_sources': map_source_sources,
                                        'fanqie': fanqie,
                                        'conflict_reason': 'new_mapping'
                                    })

                                    # 从映射中删除冲突的条目
                                    del self.zixing_mapping[map_source_hanzi]
                                    logger.info(f"映射冲突已移至待检查列表: {map_source_hanzi} -> [{existing_target}, {map_target_hanzi}]")
                                else:
                                    # 目标相同，不是冲突，只是重复确认
                                    logger.debug(f"重复确认映射关系: {map_source_hanzi} -> {map_target_hanzi}")
                            else:
                                self.zixing_mapping[map_source_hanzi] = map_target_hanzi
                                logger.info(f"建立映射关系: {map_source_hanzi} -> {map_target_hanzi} (源:{map_source_sources}, 目标:{map_target_sources})")

                    elif source1_has_xxt and not source2_has_xxt:
                        # hanzi1有xxt，hanzi2没有xxt：hanzi2 -> hanzi1（建立自动映射）
                        map_source_unicode = unicode2
                        map_target_unicode = unicode1
                        map_source_hanzi = hanzi2
                        map_target_hanzi = hanzi1
                        map_source_sources = sources2
                        map_target_sources = sources1
                        logger.info(f"映射方向2: {map_source_hanzi}({map_source_sources}) -> {map_target_hanzi}({map_target_sources})")

                        # 建立自动映射
                        if map_target_hanzi and map_source_hanzi and map_source_hanzi != map_target_hanzi:
                            # 检查是否已存在映射
                            if map_source_hanzi in self.zixing_mapping:
                                existing_target = self.zixing_mapping[map_source_hanzi]

                                # 只有当目标不同时才认为是冲突
                                if existing_target != map_target_hanzi:
                                    logger.warning(f"发现映射冲突: {map_source_hanzi} 已映射到 {existing_target}，现在又要映射到 {map_target_hanzi}")

                                    # 将冲突的映射移到待检查列表
                                    if map_source_hanzi not in self.zixing_to_check:
                                        self.zixing_to_check[map_source_hanzi] = []

                                    # 查找现有目标的unicode和sources
                                    existing_target_unicode = self.find_hanzi_unicode(existing_target)
                                    existing_target_sources = self.find_hanzi_sources(existing_target, fanqie)

                                    # 添加现有映射到待检查列表
                                    self.zixing_to_check[map_source_hanzi].append({
                                        'target_unicode': existing_target_unicode,
                                        'target_hanzi': existing_target,
                                        'target_sources': existing_target_sources,
                                        'source_sources': map_source_sources,
                                        'fanqie': fanqie,
                                        'conflict_reason': 'existing_mapping'
                                    })

                                    # 添加新映射到待检查列表
                                    self.zixing_to_check[map_source_hanzi].append({
                                        'target_unicode': map_target_unicode,
                                        'target_hanzi': map_target_hanzi,
                                        'target_sources': map_target_sources,
                                        'source_sources': map_source_sources,
                                        'fanqie': fanqie,
                                        'conflict_reason': 'new_mapping'
                                    })

                                    # 从映射中删除冲突的条目
                                    del self.zixing_mapping[map_source_hanzi]
                                    logger.info(f"映射冲突已移至待检查列表: {map_source_hanzi} -> [{existing_target}, {map_target_hanzi}]")
                                else:
                                    # 目标相同，不是冲突，只是重复确认
                                    logger.debug(f"重复确认映射关系: {map_source_hanzi} -> {map_target_hanzi}")
                            else:
                                self.zixing_mapping[map_source_hanzi] = map_target_hanzi
                                logger.info(f"建立映射关系: {map_source_hanzi} -> {map_target_hanzi} (源:{map_source_sources}, 目标:{map_target_sources})")

                    else:
                        # 两个都有xxt或都没有xxt，记录到待检查列表（不建立自动映射）
                        map_source_unicode = unicode1
                        map_target_unicode = unicode2
                        map_source_hanzi = hanzi1
                        map_target_hanzi = hanzi2
                        map_source_sources = sources1
                        map_target_sources = sources2
                        logger.info(f"映射方向3: {map_source_hanzi}({map_source_sources}) -> {map_target_hanzi}({map_target_sources})")

                        # 记录到待检查列表
                        if map_target_hanzi and map_source_hanzi and map_source_hanzi != map_target_hanzi:
                            if map_source_hanzi not in self.zixing_to_check:
                                self.zixing_to_check[map_source_hanzi] = []

                            logger.info(f"记录到待检查: {map_source_hanzi} -> {map_target_hanzi} (源:{map_source_sources}, 目标:{map_target_sources})")
                            self.zixing_to_check[map_source_hanzi].append({
                                'target_unicode': map_target_unicode,
                                'target_hanzi': map_target_hanzi,
                                'target_sources': map_target_sources,
                                'source_sources': map_source_sources,
                                'fanqie': fanqie
                            })
                else:
                    logger.debug(f"汉字对不在同一关系组中: {hanzi1}({unicode1}) vs {hanzi2}({unicode2})")

                # 添加延迟避免API调用过于频繁
                time.sleep(0.1)

                self.processed_count += 1
    
    def find_hanzi_by_unicode(self, unicode_code: str, analysis_data: Dict) -> Optional[str]:
        """根据Unicode编码查找汉字

        Args:
            unicode_code: Unicode编码
            analysis_data: 分析数据

        Returns:
            str: 汉字，如果未找到返回None
        """
        try:
            groups = analysis_data.get('groups', {})

            for fanqie, group_info in groups.items():
                hanzi_details = group_info.get('hanzi_details', [])
                for hanzi_detail in hanzi_details:
                    if hanzi_detail.get('unicode') == unicode_code:
                        return hanzi_detail.get('hanzi', '')

            return None

        except Exception as e:
            logger.error(f"根据Unicode查找汉字时发生错误: {e}")
            return None

    def find_hanzi_detail_by_unicode(self, unicode_code: str, analysis_data: Dict, fanqie_group: str = None) -> Optional[Dict]:
        """根据Unicode编码查找汉字详细信息

        Args:
            unicode_code: Unicode编码
            analysis_data: 分析数据
            fanqie_group: 指定的反切组，如果提供则优先在该组中查找

        Returns:
            Dict: 汉字详细信息，如果未找到返回None
        """
        try:
            groups = analysis_data.get('groups', {})

            # 如果指定了反切组，优先在该组中查找
            if fanqie_group and fanqie_group in groups:
                group_info = groups[fanqie_group]
                hanzi_details = group_info.get('hanzi_details', [])
                for hanzi_detail in hanzi_details:
                    if hanzi_detail.get('unicode') == unicode_code:
                        return hanzi_detail

            # 如果在指定组中没找到，或者没有指定组，则在所有组中查找
            for fanqie, group_info in groups.items():
                hanzi_details = group_info.get('hanzi_details', [])
                for hanzi_detail in hanzi_details:
                    if hanzi_detail.get('unicode') == unicode_code:
                        return hanzi_detail

            return None

        except Exception as e:
            logger.error(f"根据Unicode查找汉字详细信息时发生错误: {e}")
            return None

    def find_hanzi_detail_in_group(self, unicode_code: str, hanzi_details: List[Dict]) -> Optional[Dict]:
        """在指定的汉字详情列表中查找Unicode对应的字符

        Args:
            unicode_code: Unicode编码
            hanzi_details: 汉字详情列表

        Returns:
            Dict: 汉字详细信息，如果未找到返回None
        """
        try:
            for hanzi_detail in hanzi_details:
                if hanzi_detail.get('unicode') == unicode_code:
                    return hanzi_detail
            return None
        except Exception as e:
            logger.error(f"在组中查找汉字详细信息时发生错误: {e}")
            return None

    def load_existing_mappings(self, mapping_file: str, to_check_file: str):
        """加载现有的映射文件以实现去重

        Args:
            mapping_file: 映射文件路径
            to_check_file: 待检查文件路径
        """
        # 加载现有映射
        if os.path.exists(mapping_file):
            try:
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    self.zixing_mapping = json.load(f)
                logger.info(f"加载现有映射文件: {mapping_file}, 包含 {len(self.zixing_mapping)} 个映射")
            except Exception as e:
                logger.error(f"加载现有映射文件失败: {e}")
                self.zixing_mapping = {}

        # 重新初始化待检查数据为字典格式（忽略现有文件，因为格式可能不同）
        self.zixing_to_check = {}

    def save_results(self, mapping_file: str, to_check_file: str) -> bool:
        """保存结果到文件

        Args:
            mapping_file: 映射文件路径
            to_check_file: 待检查文件路径

        Returns:
            bool: 是否保存成功
        """
        try:
            # 保存映射关系
            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(self.zixing_mapping, f, ensure_ascii=False, indent=2)
            logger.info(f"映射关系已保存到: {mapping_file}, 包含 {len(self.zixing_mapping)} 个映射")

            # 转换待检查数据格式：按反切值分组
            to_check_by_fanqie = {}
            for source_hanzi, targets in self.zixing_to_check.items():
                for target_info in targets:
                    fanqie = target_info['fanqie']
                    if fanqie not in to_check_by_fanqie:
                        to_check_by_fanqie[fanqie] = {
                            "fanqie": fanqie,
                            "hanzis": []
                        }

                    # 添加源字符（如果不存在）
                    if not any(h['hanzi'] == source_hanzi for h in to_check_by_fanqie[fanqie]['hanzis']):
                        # 从target_info中获取源字符的正确unicode（这个信息在处理时已经保存）
                        # 如果没有，则通过查找原始数据获取
                        source_unicode = None

                        # 尝试从原始数据中查找源字符的unicode
                        groups = self.analysis_data.get('groups', {}) if hasattr(self, 'analysis_data') else {}
                        for fanqie_key, group_info in groups.items():
                            hanzi_details = group_info.get('hanzi_details', [])
                            for detail in hanzi_details:
                                if detail.get('hanzi') == source_hanzi:
                                    source_unicode = detail.get('unicode')
                                    break
                            if source_unicode:
                                break

                        # 如果还是没找到，使用字符编码（但这对多字节字符可能不准确）
                        if not source_unicode and source_hanzi:
                            # 对于单个字符，使用正确的Unicode编码
                            if len(source_hanzi) == 1:
                                source_unicode = f"{ord(source_hanzi):X}"
                            else:
                                # 对于多字符，取第一个字符的编码
                                source_unicode = f"{ord(source_hanzi[0]):X}"

                        # 获取源字符的sources信息
                        source_sources = target_info.get('source_sources', [])

                        to_check_by_fanqie[fanqie]['hanzis'].append({
                            "hanzi": source_hanzi,
                            "unicode": source_unicode,
                            "sources": source_sources
                        })

                    # 添加目标字符（如果不存在）
                    target_hanzi = target_info['target_hanzi']
                    target_unicode = target_info['target_unicode']
                    target_sources = target_info.get('target_sources', [])

                    if not any(h['hanzi'] == target_hanzi for h in to_check_by_fanqie[fanqie]['hanzis']):
                        to_check_by_fanqie[fanqie]['hanzis'].append({
                            "hanzi": target_hanzi,
                            "unicode": target_unicode,
                            "sources": target_sources
                        })

            # 转换为列表格式
            to_check_list = list(to_check_by_fanqie.values())

            # 保存待检查数据
            with open(to_check_file, 'w', encoding='utf-8') as f:
                json.dump(to_check_list, f, ensure_ascii=False, indent=2)
            logger.info(f"待检查数据已保存到: {to_check_file}, 包含 {len(to_check_list)} 个反切组")

            return True

        except Exception as e:
            logger.error(f"保存结果失败: {e}")
            return False

    def generate_summary_report(self) -> str:
        """生成摘要报告

        Returns:
            str: 摘要报告
        """
        report = f"""
=== 字形关系分析摘要报告 ===
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

处理统计:
- 已处理汉字数: {self.processed_count:,}
- 建立映射关系数: {len(self.zixing_mapping):,}
- 待检查关系数: {len(self.zixing_to_check):,}

映射关系示例（前10个）:"""

        # 显示前10个映射关系
        for i, (source, target) in enumerate(list(self.zixing_mapping.items())[:10]):
            report += f"\n- {source} -> {target}"

        if len(self.zixing_mapping) > 10:
            report += f"\n... 还有 {len(self.zixing_mapping) - 10} 个映射关系"

        # 显示待检查的关系
        if self.zixing_to_check:
            total_to_check_groups = len(set(target_info['fanqie'] for targets in self.zixing_to_check.values() for target_info in targets))
            report += f"\n\n待检查反切组数: {total_to_check_groups}"
            report += f"\n待检查关系示例（前5个）:"
            for i, (source, targets) in enumerate(list(self.zixing_to_check.items())[:5]):
                report += f"\n- {source}: {len(targets)} 个目标"
                for target_info in targets[:3]:  # 只显示前3个目标
                    report += f"\n  -> {target_info.get('target_hanzi', 'N/A')} (反切: {target_info.get('fanqie', 'N/A')})"
                if len(targets) > 3:
                    report += f"\n  ... 还有 {len(targets) - 3} 个目标"

        return report

    def run_analysis(self, input_file: str, mapping_file: str, to_check_file: str) -> bool:
        """运行完整分析流程

        Args:
            input_file: 输入分析数据文件
            mapping_file: 输出映射文件
            to_check_file: 输出待检查文件

        Returns:
            bool: 是否成功
        """
        logger.info("开始字形关系分析")

        try:
            # 1. 加载分析数据
            analysis_data = self.load_analysis_data(input_file)
            if not analysis_data:
                return False

            # 保存analysis_data的引用，供后续方法使用
            self.analysis_data = analysis_data

            # 2. 加载现有映射（实现去重）
            self.load_existing_mappings(mapping_file, to_check_file)

            # 3. 获取组数据
            groups = analysis_data.get('groups', {})
            self.total_count = sum(len(group_info.get('hanzi_details', [])) for group_info in groups.values())
            logger.info(f"总共需要处理 {len(groups)} 个反切组，包含 {self.total_count} 个汉字")

            # 4. 处理每个反切组
            for fanqie, group_info in groups.items():




                try:
                    self.process_fanqie_group(fanqie, group_info, analysis_data)
                except Exception as e:
                    logger.error(f"处理反切组 '{fanqie}' 时发生错误: {e}")
                    continue

            # 5. 保存结果
            if not self.save_results(mapping_file, to_check_file):
                return False

            # 6. 生成并显示摘要报告
            summary = self.generate_summary_report()
            logger.info(summary)

            logger.info("字形关系分析完成！")
            return True

        except Exception as e:
            logger.error(f"分析过程中发生错误: {e}")
            return False


def main():
    """主函数"""
    # 文件路径配置
    script_dir = os.path.dirname(os.path.abspath(__file__))
    input_file = os.path.join(script_dir, 'null_ref_fanqie_analysis.json')
    mapping_file = os.path.join(script_dir, 'zixing_mapping.json')
    to_check_file = os.path.join(script_dir, 'zixing_to_check.json')

    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        logger.error(f"输入文件不存在: {input_file}")
        sys.exit(1)

    # 创建分析器并运行
    analyzer = ZixingRelationAnalyzer()

    try:
        success = analyzer.run_analysis(input_file, mapping_file, to_check_file)
        if success:
            print(f"\n✅ 分析完成！")
            print(f"映射关系已保存到: {mapping_file}")
            print(f"待检查数据已保存到: {to_check_file}")
        else:
            print("\n❌ 分析失败，请查看日志了解详情")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
